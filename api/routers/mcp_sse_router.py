"""
MCP SSE (Server-Sent Events) router implementation.

This module implements the MCP protocol using Server-Sent Events for communication
with MCP clients like Cherry Studio.
"""

import asyncio
import json
import logging
from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, Request
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession

from mcp.server.sse import SseServerTransport
from mcp.server import Server
from mcp.types import Tool, TextContent

from api.database import get_db, get_session
from api.services.tool_service import ToolService
from api.errors.tool_error import ToolExecutionError, ToolNotFoundError

# Get logger
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(tags=["mcp-sse"])

# Global MCP server instance
mcp_server: Optional[Server] = None


class McpSseHandler:
    """
    MCP SSE handler for managing tool operations.
    """

    def __init__(self):
        """Initialize the MCP SSE handler."""
        self.server = Server("easy-mcp-sse")
        self._setup_handlers()

    def _setup_handlers(self):
        """Setup MCP server handlers."""

        @self.server.list_tools()
        async def list_tools() -> List[Tool]:
            """
            List all available tools.

            Returns:
                List[Tool]: List of available MCP tools
            """
            try:
                async with get_session() as db:
                    service = ToolService(db)
                    tools, _ = await service.query_tools(page=1, size=1000)

                    # Filter enabled tools and convert to MCP Tool format
                    mcp_tools = []
                    for tool in tools:
                        if tool.is_enabled:
                            try:
                                parameters = json.loads(tool.parameters)
                                mcp_tool = Tool(
                                    name=tool.name,
                                    description=tool.description or "",
                                    inputSchema=parameters,
                                )
                                mcp_tools.append(mcp_tool)
                            except json.JSONDecodeError as e:
                                logger.warning(
                                    f"Invalid parameters JSON for tool {tool.name}: {e}"
                                )
                                continue

                    logger.info(f"Listed {len(mcp_tools)} enabled tools for MCP SSE")
                    return mcp_tools

            except Exception as e:
                logger.error(f"Error listing tools for MCP SSE: {e}")
                return []

        @self.server.call_tool()
        async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """
            Execute a tool with given arguments.

            Args:
                name: Tool name
                arguments: Tool arguments

            Returns:
                List[TextContent]: Tool execution result
            """
            try:
                async with get_session() as db:
                    service = ToolService(db)

                    # Get tool by name
                    tool = await service.get_tool_by_name(name)
                    if not tool:
                        error_msg = f"Tool '{name}' not found"
                        logger.error(error_msg)
                        return [TextContent(type="text", text=error_msg)]

                    if not tool.is_enabled:
                        error_msg = f"Tool '{name}' is disabled"
                        logger.warning(error_msg)
                        return [TextContent(type="text", text=error_msg)]

                    # Execute tool
                    try:
                        result, logs = await service.execute_tool(
                            tool.id, arguments, call_type="mcp"
                        )

                        # Format result as text content
                        if result is not None:
                            # Convert result to string if it's not already
                            if isinstance(result, (dict, list)):
                                result_text = json.dumps(result, ensure_ascii=False, indent=2)
                            else:
                                result_text = str(result)
                        else:
                            result_text = "Tool executed successfully (no return value)"

                        # Add logs if available
                        if logs:
                            logs_text = "\n".join(logs)
                            result_text += f"\n\nLogs:\n{logs_text}"

                        logger.info(f"Tool '{name}' executed successfully via MCP SSE")
                        return [TextContent(type="text", text=result_text)]

                    except ToolExecutionError as e:
                        error_msg = f"Tool execution failed: {e.description}"
                        logger.error(error_msg)
                        return [TextContent(type="text", text=error_msg)]

            except Exception as e:
                error_msg = f"Unexpected error executing tool '{name}': {str(e)}"
                logger.error(error_msg)
                return [TextContent(type="text", text=error_msg)]


# Global handler instance
mcp_handler: Optional[McpSseHandler] = None


@asynccontextmanager
async def mcp_server_lifespan():
    """
    MCP server lifespan manager.
    """
    global mcp_server, mcp_handler

    try:
        # Initialize MCP handler
        mcp_handler = McpSseHandler()
        mcp_server = mcp_handler.server

        logger.info("MCP SSE server initialized successfully")
        yield

    except Exception as e:
        logger.error(f"Error initializing MCP SSE server: {e}")
        raise
    finally:
        # Cleanup
        mcp_server = None
        mcp_handler = None
        logger.info("MCP SSE server shutdown completed")


@router.get("/sse")
async def handle_sse(request: Request):
    """
    Handle MCP SSE connections.

    Args:
        request: FastAPI request object

    Returns:
        StreamingResponse: SSE stream response
    """
    if not mcp_server:
        logger.error("MCP SSE server not initialized")
        return StreamingResponse(
            iter(["data: {\"error\": \"MCP server not initialized\"}\n\n"]),
            media_type="text/plain",
        )

    # Create SSE transport
    transport = SseServerTransport("/sse")

    async def event_stream():
        """Generate SSE events."""
        try:
            # Run the MCP server with SSE transport
            async with mcp_server.run(transport) as server_context:
                logger.info("MCP SSE connection established")

                # Keep the connection alive and handle events
                try:
                    while True:
                        # Check if client disconnected
                        if await request.is_disconnected():
                            logger.info("MCP SSE client disconnected")
                            break

                        # Small delay to prevent busy waiting
                        await asyncio.sleep(0.1)

                except asyncio.CancelledError:
                    logger.info("MCP SSE connection cancelled")
                    return

        except Exception as e:
            logger.error(f"Error in MCP SSE event stream: {e}")
            yield f"data: {{\"error\": \"{str(e)}\"}}\n\n"

    return StreamingResponse(
        event_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
        },
    )