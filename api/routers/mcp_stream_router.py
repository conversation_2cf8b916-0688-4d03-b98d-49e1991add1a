"""
MCP StreamableHTTP router implementation.

This module implements the MCP protocol using StreamableHTTP transport for communication
with MCP clients that support HTTP-based streaming.
"""

import asyncio
import json
import logging
from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, Request, HTTPException
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession

from mcp.server.streamable_http import StreamableHTTPServerTransport
from mcp.server import Server
from mcp.types import Tool, TextContent

from api.database import get_db, get_session
from api.services.tool_service import ToolService
from api.errors.tool_error import ToolExecutionError, ToolNotFoundError

# Get logger
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(tags=["mcp-stream"])

# Global MCP server instance
mcp_stream_server: Optional[Server] = None


class McpStreamHandler:
    """
    MCP StreamableHTTP handler for managing tool operations.
    """

    def __init__(self):
        """Initialize the MCP Stream handler."""
        self.server = Server("easy-mcp-stream")
        self._setup_handlers()

    def _setup_handlers(self):
        """Setup MCP server handlers."""

        @self.server.list_tools()
        async def list_tools() -> List[Tool]:
            """
            List all available tools.

            Returns:
                List[Tool]: List of available MCP tools
            """
            try:
                async with get_session() as db:
                    service = ToolService(db)
                    tools, _ = await service.query_tools(page=1, size=1000)

                    # Filter enabled tools and convert to MCP Tool format
                    mcp_tools = []
                    for tool in tools:
                        if tool.is_enabled:
                            try:
                                parameters = json.loads(tool.parameters)
                                mcp_tool = Tool(
                                    name=tool.name,
                                    description=tool.description or "",
                                    inputSchema=parameters,
                                )
                                mcp_tools.append(mcp_tool)
                            except json.JSONDecodeError as e:
                                logger.warning(
                                    f"Invalid parameters JSON for tool {tool.name}: {e}"
                                )
                                continue

                    logger.info(f"Listed {len(mcp_tools)} enabled tools for MCP Stream")
                    return mcp_tools

            except Exception as e:
                logger.error(f"Error listing tools for MCP Stream: {e}")
                return []

        @self.server.call_tool()
        async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """
            Execute a tool with given arguments.

            Args:
                name: Tool name
                arguments: Tool arguments

            Returns:
                List[TextContent]: Tool execution result
            """
            try:
                async with get_session() as db:
                    service = ToolService(db)

                    # Get tool by name
                    tool = await service.get_tool_by_name(name)
                    if not tool:
                        error_msg = f"Tool '{name}' not found"
                        logger.error(error_msg)
                        return [TextContent(type="text", text=error_msg)]

                    if not tool.is_enabled:
                        error_msg = f"Tool '{name}' is disabled"
                        logger.warning(error_msg)
                        return [TextContent(type="text", text=error_msg)]

                    # Execute tool
                    try:
                        result, logs = await service.execute_tool(
                            tool.id, arguments, call_type="mcp"
                        )

                        # Format result as text content
                        if result is not None:
                            # Convert result to string if it's not already
                            if isinstance(result, (dict, list)):
                                result_text = json.dumps(result, ensure_ascii=False, indent=2)
                            else:
                                result_text = str(result)
                        else:
                            result_text = "Tool executed successfully (no return value)"

                        # Add logs if available
                        if logs:
                            logs_text = "\n".join(logs)
                            result_text += f"\n\nLogs:\n{logs_text}"

                        logger.info(f"Tool '{name}' executed successfully via MCP Stream")
                        return [TextContent(type="text", text=result_text)]

                    except ToolExecutionError as e:
                        error_msg = f"Tool execution failed: {e.description}"
                        logger.error(error_msg)
                        return [TextContent(type="text", text=error_msg)]

            except Exception as e:
                error_msg = f"Unexpected error executing tool '{name}': {str(e)}"
                logger.error(error_msg)
                return [TextContent(type="text", text=error_msg)]


# Global handler instance
mcp_stream_handler: Optional[McpStreamHandler] = None


@asynccontextmanager
async def mcp_stream_server_lifespan():
    """
    MCP Stream server lifespan manager.
    """
    global mcp_stream_server, mcp_stream_handler

    try:
        # Initialize MCP handler
        mcp_stream_handler = McpStreamHandler()
        mcp_stream_server = mcp_stream_handler.server

        logger.info("MCP Stream server initialized successfully")
        yield

    except Exception as e:
        logger.error(f"Error initializing MCP Stream server: {e}")
        raise
    finally:
        # Cleanup
        mcp_stream_server = None
        mcp_stream_handler = None
        logger.info("MCP Stream server shutdown completed")


@router.post("/stream")
async def handle_stream(request: Request):
    """
    Handle MCP StreamableHTTP connections.

    Args:
        request: FastAPI request object

    Returns:
        dict: JSON response
    """
    if not mcp_stream_server:
        logger.error("MCP Stream server not initialized")
        raise HTTPException(
            status_code=500, detail="MCP Stream server not initialized"
        )

    try:
        # Get request body
        body = await request.body()

        # Process the request body if provided
        if body:
            try:
                # Parse JSON-RPC request
                request_data = json.loads(body.decode('utf-8'))
                logger.debug(f"Received MCP Stream request: {request_data}")

                # Handle different MCP methods
                method = request_data.get('method')
                params = request_data.get('params', {})
                request_id = request_data.get('id')

                if method == 'tools/list':
                    # List tools using our handler
                    async with get_session() as db:
                        service = ToolService(db)
                        tools, _ = await service.query_tools(page=1, size=1000)

                        # Filter enabled tools and convert to response format
                        tool_list = []
                        for tool in tools:
                            if tool.is_enabled:
                                try:
                                    parameters = json.loads(tool.parameters)
                                    tool_list.append({
                                        "name": tool.name,
                                        "description": tool.description or "",
                                        "inputSchema": parameters
                                    })
                                except json.JSONDecodeError:
                                    continue

                        response = {
                            "jsonrpc": "2.0",
                            "id": request_id,
                            "result": {
                                "tools": tool_list
                            }
                        }
                        return response

                elif method == 'tools/call':
                    # Call tool using our handler
                    tool_name = params.get('name')
                    arguments = params.get('arguments', {})

                    if tool_name:
                        async with get_session() as db:
                            service = ToolService(db)

                            # Get tool by name
                            tool = await service.get_tool_by_name(tool_name)
                            if not tool:
                                return {
                                    "jsonrpc": "2.0",
                                    "id": request_id,
                                    "error": {
                                        "code": -32602,
                                        "message": f"Tool '{tool_name}' not found"
                                    }
                                }
                            elif not tool.is_enabled:
                                return {
                                    "jsonrpc": "2.0",
                                    "id": request_id,
                                    "error": {
                                        "code": -32602,
                                        "message": f"Tool '{tool_name}' is disabled"
                                    }
                                }
                            else:
                                try:
                                    result, logs = await service.execute_tool(
                                        tool.id, arguments, call_type="mcp"
                                    )

                                    # Format result as text content
                                    if result is not None:
                                        if isinstance(result, (dict, list)):
                                            result_text = json.dumps(result, ensure_ascii=False, indent=2)
                                        else:
                                            result_text = str(result)
                                    else:
                                        result_text = "Tool executed successfully (no return value)"

                                    # Add logs if available
                                    if logs:
                                        logs_text = "\n".join(logs)
                                        result_text += f"\n\nLogs:\n{logs_text}"

                                    return {
                                        "jsonrpc": "2.0",
                                        "id": request_id,
                                        "result": {
                                            "content": [
                                                {
                                                    "type": "text",
                                                    "text": result_text
                                                }
                                            ]
                                        }
                                    }

                                except ToolExecutionError as e:
                                    return {
                                        "jsonrpc": "2.0",
                                        "id": request_id,
                                        "error": {
                                            "code": -32603,
                                            "message": f"Tool execution failed: {e.description}"
                                        }
                                    }
                    else:
                        return {
                            "jsonrpc": "2.0",
                            "id": request_id,
                            "error": {
                                "code": -32602,
                                "message": "Invalid params: tool name required"
                            }
                        }

                else:
                    # Unknown method
                    return {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "error": {
                            "code": -32601,
                            "message": f"Method not found: {method}"
                        }
                    }

            except json.JSONDecodeError as e:
                return {
                    "jsonrpc": "2.0",
                    "id": None,
                    "error": {
                        "code": -32700,
                        "message": f"Parse error: {str(e)}"
                    }
                }

        else:
            # No body provided, return server info
            return {
                "jsonrpc": "2.0",
                "result": {
                    "server": "easy-mcp-stream",
                    "version": "1.0.0",
                    "capabilities": {
                        "tools": True
                    }
                }
            }

    except Exception as e:
        logger.error(f"Error handling MCP Stream request: {e}")
        return {
            "jsonrpc": "2.0",
            "error": {
                "code": -32603,
                "message": f"Internal error: {str(e)}"
            }
        }

    except Exception as e:
        logger.error(f"Error handling MCP Stream request: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.options("/stream")
async def handle_stream_options():
    """
    Handle CORS preflight requests for MCP Stream.

    Returns:
        dict: Empty response with CORS headers
    """
    return {}


@router.get("/stream/info")
async def get_stream_info():
    """
    Get MCP Stream server information.

    Returns:
        dict: Server information
    """
    if not mcp_stream_server:
        raise HTTPException(
            status_code=500, detail="MCP Stream server not initialized"
        )

    return {
        "server": "easy-mcp-stream",
        "version": "1.0.0",
        "transport": "streamable-http",
        "capabilities": {
            "tools": True
        },
        "status": "running"
    }