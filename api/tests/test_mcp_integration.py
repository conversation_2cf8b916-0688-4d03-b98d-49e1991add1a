"""
Test MCP integration functionality.

This module tests the MCP SSE and StreamableHTTP implementations.
"""

import asyncio
import json
import pytest
from unittest.mock import AsyncMock, patch

from api.routers.mcp_sse_router import Mcp<PERSON><PERSON><PERSON>andler, mcp_server_lifespan
from api.routers.mcp_stream_router import McpStreamHandler, mcp_stream_server_lifespan
from api.services.tool_service import ToolService
from api.models.tb_tool import TbTool


class TestMcpSseHandler:
    """Test MCP SSE handler functionality."""

    @pytest.fixture
    def mcp_handler(self):
        """Create MCP SSE handler for testing."""
        return McpSseHandler()

    @pytest.mark.asyncio
    async def test_list_tools_handler_setup(self, mcp_handler):
        """Test that list_tools handler is properly set up."""
        assert mcp_handler.server is not None
        assert hasattr(mcp_handler.server, '_list_tools_handler')

    @pytest.mark.asyncio
    async def test_call_tool_handler_setup(self, mcp_handler):
        """Test that call_tool handler is properly set up."""
        assert mcp_handler.server is not None
        assert hasattr(mcp_handler.server, '_call_tool_handler')


class TestMcpStreamHandler:
    """Test MCP StreamableHTTP handler functionality."""

    @pytest.fixture
    def mcp_stream_handler(self):
        """Create MCP Stream handler for testing."""
        return McpStreamHandler()

    @pytest.mark.asyncio
    async def test_list_tools_handler_setup(self, mcp_stream_handler):
        """Test that list_tools handler is properly set up."""
        assert mcp_stream_handler.server is not None
        assert hasattr(mcp_stream_handler.server, '_list_tools_handler')

    @pytest.mark.asyncio
    async def test_call_tool_handler_setup(self, mcp_stream_handler):
        """Test that call_tool handler is properly set up."""
        assert mcp_stream_handler.server is not None
        assert hasattr(mcp_stream_handler.server, '_call_tool_handler')


class TestMcpLifespan:
    """Test MCP server lifespan management."""

    @pytest.mark.asyncio
    async def test_mcp_sse_lifespan(self):
        """Test MCP SSE server lifespan."""
        try:
            async with mcp_server_lifespan():
                # Server should be initialized
                from api.routers.mcp_sse_router import mcp_server
                assert mcp_server is not None
        except Exception as e:
            pytest.fail(f"MCP SSE lifespan failed: {e}")

    @pytest.mark.asyncio
    async def test_mcp_stream_lifespan(self):
        """Test MCP Stream server lifespan."""
        try:
            async with mcp_stream_server_lifespan():
                # Server should be initialized
                from api.routers.mcp_stream_router import mcp_stream_server
                assert mcp_stream_server is not None
        except Exception as e:
            pytest.fail(f"MCP Stream lifespan failed: {e}")


class TestMcpIntegration:
    """Test MCP integration with ToolService."""

    @pytest.mark.asyncio
    @patch('api.routers.mcp_sse_router.get_session')
    async def test_tool_service_integration(self, mock_get_session):
        """Test integration with ToolService."""
        # Mock database session
        mock_db = AsyncMock()
        mock_get_session.return_value.__aenter__.return_value = mock_db

        # Mock ToolService
        mock_service = AsyncMock(spec=ToolService)
        mock_tool = TbTool(
            id=1,
            name="test_tool",
            description="Test tool",
            parameters='{"type": "object", "properties": {}}',
            code="result = 'test'",
            is_enabled=True
        )
        mock_service.query_tools.return_value = ([mock_tool], 1)
        mock_service.execute_tool.return_value = ("test result", ["log1", "log2"])

        with patch('api.routers.mcp_sse_router.ToolService', return_value=mock_service):
            handler = McpSseHandler()
            
            # Test list_tools functionality
            tools = await handler.server._list_tools_handler()
            assert len(tools) == 1
            assert tools[0].name == "test_tool"
            assert tools[0].description == "Test tool"

            # Test call_tool functionality  
            result = await handler.server._call_tool_handler("test_tool", {})
            assert len(result) == 1
            assert result[0].type == "text"
            assert "test result" in result[0].text


if __name__ == "__main__":
    # Run basic tests
    async def run_basic_tests():
        """Run basic functionality tests."""
        print("🧪 Testing MCP SSE Handler...")
        sse_handler = McpSseHandler()
        assert sse_handler.server is not None
        print("✅ MCP SSE Handler created successfully")

        print("🧪 Testing MCP Stream Handler...")
        stream_handler = McpStreamHandler()
        assert stream_handler.server is not None
        print("✅ MCP Stream Handler created successfully")

        print("🧪 Testing MCP SSE Lifespan...")
        try:
            async with mcp_server_lifespan():
                print("✅ MCP SSE Lifespan works correctly")
        except Exception as e:
            print(f"❌ MCP SSE Lifespan failed: {e}")

        print("🧪 Testing MCP Stream Lifespan...")
        try:
            async with mcp_stream_server_lifespan():
                print("✅ MCP Stream Lifespan works correctly")
        except Exception as e:
            print(f"❌ MCP Stream Lifespan failed: {e}")

        print("🎉 All basic tests passed!")

    asyncio.run(run_basic_tests())
