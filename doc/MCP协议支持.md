# MCP 协议支持文档

Easy MCP 现在支持两种 MCP (Model Context Protocol) 传输方式，可以与支持 MCP 协议的客户端（如 Cherry Studio）进行通信。

## 支持的传输方式

### 1. MCP SSE (Server-Sent Events)

**端点**: `GET /sse`

**特点**:
- 基于 HTTP Server-Sent Events 的实时通信
- 适合需要实时推送的场景
- 支持长连接，低延迟

**使用方式**:
```bash
# 连接到 MCP SSE 端点
curl -N -H "Accept: text/event-stream" http://localhost:8000/sse
```

### 2. MCP StreamableHTTP

**端点**: `POST /stream`

**特点**:
- 基于 HTTP 的流式传输
- 支持 JSON-RPC 2.0 协议
- 适合批量操作和复杂交互

**使用方式**:
```bash
# 获取服务器信息
curl -X POST http://localhost:8000/stream

# 列出所有工具
curl -X POST http://localhost:8000/stream \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/list",
    "id": 1
  }'

# 调用工具
curl -X POST http://localhost:8000/stream \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
      "name": "tool_name",
      "arguments": {"param1": "value1"}
    },
    "id": 2
  }'
```

**服务器信息端点**: `GET /stream/info`
```bash
curl http://localhost:8000/stream/info
```

## 工具集成

### 工具列表

两种传输方式都会自动获取系统中启用的工具：

1. 使用 `ToolService.query_tools()` 方法获取所有工具
2. 过滤出 `is_enabled=True` 的工具
3. 将工具参数从 JSON 字符串解析为 JSON Schema 格式
4. 返回符合 MCP 协议的工具列表

### 工具执行

工具执行流程：

1. 根据工具名称查找工具
2. 验证工具是否存在且已启用
3. 使用 `ToolService.execute_tool()` 方法执行工具
4. 格式化执行结果和日志
5. 返回符合 MCP 协议的响应

## 错误处理

### SSE 错误处理
- 连接错误：返回 SSE 错误事件
- 工具执行错误：在结果中包含错误信息
- 服务器错误：记录日志并返回通用错误

### StreamableHTTP 错误处理
- JSON-RPC 2.0 标准错误码：
  - `-32700`: 解析错误
  - `-32601`: 方法未找到
  - `-32602`: 无效参数
  - `-32603`: 内部错误

## 配置说明

### 服务器配置

MCP 服务器在应用启动时自动初始化：

```python
# 在 main.py 中的生命周期管理
async with mcp_server_lifespan():
    async with mcp_stream_server_lifespan():
        yield
```

### CORS 配置

StreamableHTTP 支持 CORS：
- `Access-Control-Allow-Origin: *`
- `Access-Control-Allow-Headers: Content-Type`
- `Access-Control-Allow-Methods: POST, OPTIONS`

## 客户端集成示例

### Cherry Studio 集成

1. 在 Cherry Studio 中添加 MCP 服务器
2. 配置服务器地址：`http://localhost:8000`
3. 选择传输方式：SSE 或 StreamableHTTP
4. 连接后即可使用系统中的工具

### 自定义客户端

```python
import asyncio
import aiohttp
import json

async def test_mcp_stream():
    """测试 MCP StreamableHTTP"""
    async with aiohttp.ClientSession() as session:
        # 列出工具
        async with session.post(
            'http://localhost:8000/stream',
            json={
                "jsonrpc": "2.0",
                "method": "tools/list",
                "id": 1
            }
        ) as resp:
            data = await resp.text()
            print("Tools:", data)
        
        # 调用工具
        async with session.post(
            'http://localhost:8000/stream',
            json={
                "jsonrpc": "2.0",
                "method": "tools/call",
                "params": {
                    "name": "your_tool_name",
                    "arguments": {"param": "value"}
                },
                "id": 2
            }
        ) as resp:
            data = await resp.text()
            print("Result:", data)

# 运行测试
asyncio.run(test_mcp_stream())
```

## 监控和调试

### 日志记录

- 所有 MCP 操作都会记录详细日志
- 工具执行会记录到工具调用日志表
- 错误信息会记录到系统日志

### 调试信息

- 使用 `call_type="mcp"` 标识 MCP 调用
- 执行结果包含工具输出和日志信息
- 支持实时监控工具执行状态

## 性能优化

### 连接管理
- SSE 连接支持自动断线检测
- StreamableHTTP 支持连接复用
- 合理的超时和重试机制

### 资源管理
- 自动清理断开的连接
- 内存使用优化
- 异步处理提高并发性能

## 故障排除

### 常见问题

1. **连接失败**
   - 检查服务器是否正常运行
   - 验证端口和地址配置
   - 查看服务器日志

2. **工具执行失败**
   - 确认工具已启用
   - 检查工具参数格式
   - 查看工具执行日志

3. **协议错误**
   - 验证 JSON-RPC 格式
   - 检查方法名称和参数
   - 确认客户端兼容性

### 调试命令

```bash
# 检查服务器状态
curl http://localhost:8000/api/v1/system

# 获取工具列表
curl http://localhost:8000/api/v1/tool-mcp

# 测试工具执行
curl -X POST http://localhost:8000/api/v1/tool-mcp/tool_name/execute \
  -H "Content-Type: application/json" \
  -d '{"parameters": {"param": "value"}}'
```

## 更新日志

- **v1.0.0**: 初始实现 MCP SSE 和 StreamableHTTP 支持
- 集成 ToolService 进行工具管理
- 支持实时工具列表和执行
- 完整的错误处理和日志记录
